#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Syndic Manager - Version 1.0
Application for managing homeowners association contributions and expenses
"""

import sys
import io
from syndic_manager_en import main

if __name__ == "__main__":
    # Fix for Windows Command Prompt encoding issues
    try:
        # Try to set stdout to use utf-8 encoding
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    except:
        # Fallback if encoding fails
        pass
        
    print("Welcome to Syndic Manager - Version 1.0")
    print("Application for managing homeowners association contributions and expenses")
    print("=" * 60)
    main()