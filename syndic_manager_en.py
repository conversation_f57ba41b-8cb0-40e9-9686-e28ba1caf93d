import json
import os
import io
import sys

class Resident:
    def __init__(self, resident_id, full_name, unit_id, phone_number, email, balance=0):
        self.resident_id = resident_id
        self.full_name = full_name
        self.unit_id = unit_id
        self.phone_number = phone_number
        self.email = email
        self.balance = float(balance) if balance else 0
        
    def update_balance(self, amount):
        """Update resident balance"""
        self.balance += float(amount)
        
    def get_details(self):
        """Return resident details as a dictionary"""
        return {
            "resident_id": self.resident_id,
            "full_name": self.full_name,
            "unit_id": self.unit_id,
            "phone_number": self.phone_number,
            "email": self.email,
            "balance": self.balance
        }

DATA_FILE = "syndic_data.json"

class SyndicManager:
    def __init__(self):
        self.residents = []
        self.next_resident_id = 1
        self.expenses = [] # List of expenses
        self.next_expense_id = 1
        self._load_data()

    def add_resident(self, full_name, unit_id, phone_number, email):
        # Check if a resident with the same unit ID already exists
        for resident in self.residents:
            if resident.unit_id == unit_id:
                print(f"Error: There is already a resident in unit {unit_id}.")
                return None
                
        resident = Resident(self.next_resident_id, full_name, unit_id, phone_number, email)
        self.residents.append(resident)
        self.next_resident_id += 1
        print(f"Resident added: {full_name} (ID: {resident.resident_id}) successfully.")
        self._save_data()
        return resident

    def find_resident_by_id(self, resident_id_str):
        """Find a resident by their ID"""
        try:
            resident_id = int(resident_id_str)
            for resident in self.residents:
                if resident.resident_id == resident_id:
                    return resident
            print(f"No resident found with ID {resident_id}.")
            return None
        except ValueError:
            print("ID must be a number.")
            return None
    
    def list_residents(self):
        """Display the list of residents"""
        if not self.residents:
            print("No residents currently registered.")
            return
            
        print("\n----- Resident List -----")
        print("ID | Full Name | Unit ID | Phone Number | Email | Balance (MAD)")
        print("-" * 80)
        for resident in self.residents:
            print(f"{resident.resident_id} | {resident.full_name} | {resident.unit_id} | "
                  f"{resident.phone_number} | {resident.email} | {resident.balance:.2f}")
        print("-" * 80)
        
    def record_contribution(self, resident_id_str, amount_str, description="Regular contribution"):
        """Record a financial contribution from a resident"""
        resident = self.find_resident_by_id(resident_id_str)
        if resident:
            try:
                amount = float(amount_str)
                if amount <= 0:
                    print("Amount must be greater than zero.")
                    return
                resident.update_balance(amount) # Contributions increase the balance
                print(f"Contribution recorded: {amount:.2f} MAD from resident {resident.full_name}. New balance: {resident.balance:.2f} MAD.")
                self._save_data()
            except ValueError:
                print("Amount must be a number.")
    
    def add_expense(self, description, amount_str, date):
        """Add a new expense"""
        try:
            amount = float(amount_str)
            if amount <= 0:
                print("Amount must be greater than zero.")
                return
                
            expense = {
                "expense_id": self.next_expense_id,
                "description": description,
                "amount": amount,
                "date": date
            }
            self.expenses.append(expense)
            self.next_expense_id += 1
            print(f"New expense recorded: '{description}' for {amount:.2f} MAD on {date}.")
            self._save_data()
        except ValueError:
            print("Amount must be a number.")
    
    def list_expenses(self):
        """Display the list of expenses"""
        if not self.expenses:
            print("No expenses currently recorded.")
            return
            
        print("\n----- Expense List -----")
        print("ID | Description | Amount (MAD) | Date")
        print("-" * 60)
        for expense in self.expenses:
            print(f"{expense['expense_id']} | {expense['description']} | {expense['amount']:.2f} | {expense['date']}")
        print("-" * 60)
        
    def generate_financial_report(self):
        """Generate a financial report"""
        total_contributions = sum(resident.balance for resident in self.residents)
        total_expenses = sum(expense["amount"] for expense in self.expenses)
        balance = total_contributions - total_expenses
        
        print("\n===== Financial Report =====")
        print(f"Total Contributions: {total_contributions:.2f} MAD")
        print(f"Total Expenses: {total_expenses:.2f} MAD")
        print(f"Fund Balance: {balance:.2f} MAD")
        print("--------------------")

    def _save_data(self):
        """Save current data (residents and expenses) to a JSON file."""
        data_to_save = {
            "residents": [r.get_details() for r in self.residents],
            "expenses": self.expenses,
            "next_resident_id": self.next_resident_id,
            "next_expense_id": self.next_expense_id
        }
        try:
            with open(DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=4)
            # print("Data saved successfully.") # Uncomment for debugging
        except IOError as e:
            print(f"Error saving data: {e}")

    def _load_data(self):
        """Load data from JSON file when the program starts."""
        if not os.path.exists(DATA_FILE):
            print("Data file does not exist. Starting with empty data.")
            return

        try:
            with open(DATA_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.residents = [Resident(**r_data) for r_data in data.get("residents", [])]
                self.expenses = data.get("expenses", [])
                self.next_resident_id = data.get("next_resident_id", 1)
                self.next_expense_id = data.get("next_expense_id", 1)
                # print("Data loaded successfully.") # Uncomment for debugging
        except (IOError, json.JSONDecodeError) as e:
            print(f"Error loading data: {e}. Starting with empty data.")
            # In case of error, start with empty data to avoid program crashes
            self.residents, self.expenses, self.next_resident_id, self.next_expense_id = [], [], 1, 1

def print_menu():
    print("\n--- Syndic Management Program Menu ---")
    print("1. Add new resident")
    print("2. Display resident list")
    print("3. Record financial contribution")
    print("4. Add new expense")
    print("5. Display expense list")
    print("6. Display financial report")
    print("0. Exit")
    print("----------------------------")

def get_user_input(prompt):
    """Get user input with error handling"""
    try:
        return input(prompt)
    except EOFError:
        print("\nInput error. Please try again.")
        return ""

def main():
    manager = SyndicManager()
 
    while True:
        print_menu()
        choice = get_user_input("Choose operation number: ")
        
        if choice == "1":
            full_name = get_user_input("Full name: ")
            unit_id = get_user_input("Unit ID (apartment): ")
            phone_number = get_user_input("Phone number: ")
            email = get_user_input("Email: ")
            manager.add_resident(full_name, unit_id, phone_number, email)
            
        elif choice == "2":
            manager.list_residents()
            
        elif choice == "3":
            resident_id = get_user_input("Resident ID: ")
            amount = get_user_input("Amount (in MAD): ")
            description = get_user_input("Description (optional): ") or "Regular contribution"
            manager.record_contribution(resident_id, amount, description)
            
        elif choice == "4":
            description = get_user_input("Expense description: ")
            amount = get_user_input("Amount (in MAD): ")
            date = get_user_input("Date (YYYY-MM-DD): ")
            manager.add_expense(description, amount, date)
            
        elif choice == "5":
            manager.list_expenses()
            
        elif choice == "6":
            manager.generate_financial_report()
            
        elif choice == "0":
            print("Thank you for using the Syndic Management Program. Goodbye!")
            break
            
        else:
            print("Invalid choice. Please select a number from the menu.")

if __name__ == "__main__":
    main()