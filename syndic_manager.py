import json
import os
import io
import sys

class Resident:
    def __init__(self, resident_id, full_name, unit_id, phone_number, email, balance=0):
        self.resident_id = resident_id
        self.full_name = full_name
        self.unit_id = unit_id
        self.phone_number = phone_number
        self.email = email
        self.balance = float(balance) if balance else 0
        
    def update_balance(self, amount):
        """تحديث رصيد الساكن"""
        self.balance += float(amount)
        
    def get_details(self):
        """إرجاع تفاصيل الساكن كقاموس"""
        return {
            "resident_id": self.resident_id,
            "full_name": self.full_name,
            "unit_id": self.unit_id,
            "phone_number": self.phone_number,
            "email": self.email,
            "balance": self.balance
        }

DATA_FILE = "syndic_data.json"

class SyndicManager:
    def __init__(self):
        self.residents = []
        self.next_resident_id = 1
        self.expenses = [] # قائمة المصروفات
        self.next_expense_id = 1
        self._load_data()

    def add_resident(self, full_name, unit_id, phone_number, email):
        # التحقق من عدم وجود ساكن بنفس رقم الوحدة لتجنب التكرار
        for resident in self.residents:
            if resident.unit_id == unit_id:
                print(f"خطأ: يوجد بالفعل ساكن في الوحدة {unit_id}.")
                return None
                
        resident = Resident(self.next_resident_id, full_name, unit_id, phone_number, email)
        self.residents.append(resident)
        self.next_resident_id += 1
        print(f"تمت إضافة الساكن: {full_name} (المعرف: {resident.resident_id}) بنجاح.")
        self._save_data()
        return resident

    def find_resident_by_id(self, resident_id_str):
        """البحث عن ساكن باستخدام معرفه"""
        try:
            resident_id = int(resident_id_str)
            for resident in self.residents:
                if resident.resident_id == resident_id:
                    return resident
            print(f"لم يتم العثور على ساكن بالمعرف {resident_id}.")
            return None
        except ValueError:
            print("المعرف يجب أن يكون رقماً.")
            return None
    
    def list_residents(self):
        """عرض قائمة السكان"""
        if not self.residents:
            print("لا يوجد سكان مسجلين حالياً.")
            return
            
        print("\n----- قائمة السكان -----")
        print("المعرف | الاسم الكامل | رقم الوحدة | رقم الهاتف | البريد الإلكتروني | الرصيد (د.م.)")
        print("-" * 80)
        for resident in self.residents:
            print(f"{resident.resident_id} | {resident.full_name} | {resident.unit_id} | "
                  f"{resident.phone_number} | {resident.email} | {resident.balance:.2f}")
        print("-" * 80)
        
    def record_contribution(self, resident_id_str, amount_str, description="مساهمة عادية"):
        """تسجيل مساهمة مالية من ساكن"""
        resident = self.find_resident_by_id(resident_id_str)
        if resident:
            try:
                amount = float(amount_str)
                if amount <= 0:
                    print("المبلغ يجب أن يكون أكبر من الصفر.")
                    return
                resident.update_balance(amount) # المساهمات تزيد الرصيد
                print(f"تم تسجيل مساهمة بقيمة {amount:.2f} د.م. من الساكن {resident.full_name}. الرصيد الجديد: {resident.balance:.2f} د.م.")
                self._save_data()
            except ValueError:
                print("المبلغ يجب أن يكون رقماً.")
    
    def add_expense(self, description, amount_str, date):
        """إضافة مصروف جديد"""
        try:
            amount = float(amount_str)
            if amount <= 0:
                print("المبلغ يجب أن يكون أكبر من الصفر.")
                return
                
            expense = {
                "expense_id": self.next_expense_id,
                "description": description,
                "amount": amount,
                "date": date
            }
            self.expenses.append(expense)
            self.next_expense_id += 1
            print(f"تم تسجيل مصروف جديد: '{description}' بقيمة {amount:.2f} د.م. بتاريخ {date}.")
            self._save_data()
        except ValueError:
            print("المبلغ يجب أن يكون رقماً.")
    
    def list_expenses(self):
        """عرض قائمة المصروفات"""
        if not self.expenses:
            print("لا توجد مصروفات مسجلة حالياً.")
            return
            
        print("\n----- قائمة المصروفات -----")
        print("المعرف | الوصف | المبلغ (د.م.) | التاريخ")
        print("-" * 60)
        for expense in self.expenses:
            print(f"{expense['expense_id']} | {expense['description']} | {expense['amount']:.2f} | {expense['date']}")
        print("-" * 60)
        
    def generate_financial_report(self):
        """إنشاء تقرير مالي"""
        total_contributions = sum(resident.balance for resident in self.residents)
        total_expenses = sum(expense["amount"] for expense in self.expenses)
        balance = total_contributions - total_expenses
        
        print("\n===== التقرير المالي =====")
        print(f"إجمالي المساهمات: {total_contributions:.2f} د.م.")
        print(f"إجمالي المصروفات: {total_expenses:.2f} د.م.")
        print(f"رصيد الصندوق: {balance:.2f} د.م.")
        # يمكن تطوير هذا الجزء لاحقاً لحساب رصيد الصندوق.
        print("--------------------")

    def _save_data(self):
        """يحفظ البيانات الحالية (السكان والمصروفات) في ملف JSON."""
        data_to_save = {
            "residents": [r.get_details() for r in self.residents],
            "expenses": self.expenses,
            "next_resident_id": self.next_resident_id,
            "next_expense_id": self.next_expense_id
        }
        try:
            with open(DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=4)
            # print("تم حفظ البيانات بنجاح.") # يمكن تفعيل هذا للتحقق
        except IOError as e:
            print(f"خطأ أثناء حفظ البيانات: {e}")

    def _load_data(self):
        """يحمل البيانات من ملف JSON عند بدء تشغيل البرنامج."""
        if not os.path.exists(DATA_FILE):
            print("ملف البيانات غير موجود. سيتم البدء ببيانات فارغة.")
            return

        try:
            with open(DATA_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.residents = [Resident(**r_data) for r_data in data.get("residents", [])]
                self.expenses = data.get("expenses", [])
                self.next_resident_id = data.get("next_resident_id", 1)
                self.next_expense_id = data.get("next_expense_id", 1)
                # print("تم تحميل البيانات بنجاح.") # يمكن تفعيل هذا للتحقق
        except (IOError, json.JSONDecodeError) as e:
            print(f"خطأ أثناء تحميل البيانات: {e}. سيتم البدء ببيانات فارغة.")
            # في حالة الخطأ، نبدأ ببيانات فارغة لتجنب تعطل البرنامج
            self.residents, self.expenses, self.next_resident_id, self.next_expense_id = [], [], 1, 1

def print_menu():
    print("\n--- قائمة برنامج تسيير السانديك ---")
    print("1. إضافة ساكن جديد")
    print("2. عرض قائمة السكان")
    print("3. تسجيل مساهمة مالية")
    print("4. إضافة مصروف جديد")
    print("5. عرض قائمة المصروفات")
    print("6. عرض التقرير المالي")
    print("0. خروج")
    print("----------------------------")

def get_user_input(prompt):
    """الحصول على مدخلات المستخدم مع معالجة أخطاء الإدخال"""
    try:
        # Try to display prompt in Arabic
        try:
            print(prompt, end="", flush=True)
            return input()
        except UnicodeEncodeError:
            # If Arabic display fails, use English alternative
            english_prompts = {
                "اختر رقم العملية: ": "Choose operation number: ",
                "الاسم الكامل: ": "Full name: ",
                "رقم الوحدة (الشقة): ": "Unit ID (apartment): ",
                "رقم الهاتف: ": "Phone number: ",
                "البريد الإلكتروني: ": "Email: ",
                "معرف الساكن: ": "Resident ID: ",
                "المبلغ (بالدرهم): ": "Amount (in MAD): ",
                "الوصف (اختياري): ": "Description (optional): ",
                "وصف المصروف: ": "Expense description: ",
                "التاريخ (YYYY-MM-DD): ": "Date (YYYY-MM-DD): "
            }
            english_prompt = english_prompts.get(prompt, prompt)
            return input(english_prompt)
    except EOFError:
        # Try both Arabic and English error message
        try:
            print("\nخطأ في القراءة. المرجو المحاولة مرة أخرى.")
        except:
            print("\nInput error. Please try again.")
        return ""

def main():
    # Fix for Windows Command Prompt encoding issues
    try:
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    except:
        pass  # If this fails, we'll handle it in other parts of the code
        
    manager = SyndicManager()

    # تعليق البيانات الأولية للتجربة في الاستخدام الفعلي
    """
    # إضافة بعض البيانات الأولية للتجربة
    res1 = manager.add_resident("أحمد العلوي", "شقة 101", "0611223344", "<EMAIL>")
    res2 = manager.add_resident("فاطمة الزهراء", "شقة 102", "0655667788", "<EMAIL>")
    
    if res1: manager.record_contribution(str(res1.resident_id), "500", "اشتراك شهري")
    if res2: manager.record_contribution(str(res2.resident_id), "450", "اشتراك شهري")
    
    manager.add_expense("تنظيف السلالم", "150", "2023-10-15")
    manager.add_expense("إصلاح باب المدخل", "300", "2023-10-20")
    """
 
    while True:
        print_menu()
        choice = get_user_input("اختر رقم العملية: ")
        
        if choice == "1":
            full_name = get_user_input("الاسم الكامل: ")
            unit_id = get_user_input("رقم الوحدة (الشقة): ")
            phone_number = get_user_input("رقم الهاتف: ")
            email = get_user_input("البريد الإلكتروني: ")
            manager.add_resident(full_name, unit_id, phone_number, email)
            
        elif choice == "2":
            manager.list_residents()
            
        elif choice == "3":
            resident_id = get_user_input("معرف الساكن: ")
            amount = get_user_input("المبلغ (بالدرهم): ")
            description = get_user_input("الوصف (اختياري): ") or "مساهمة عادية"
            manager.record_contribution(resident_id, amount, description)
            
        elif choice == "4":
            description = get_user_input("وصف المصروف: ")
            amount = get_user_input("المبلغ (بالدرهم): ")
            date = get_user_input("التاريخ (YYYY-MM-DD): ")
            manager.add_expense(description, amount, date)
            
        elif choice == "5":
            manager.list_expenses()
            
        elif choice == "6":
            manager.generate_financial_report()
            
        elif choice == "0":
            print("شكرا لاستخدام برنامج تسيير السانديك. مع السلامة!")
            break
            
        else:
            print("اختيار غير صالح. المرجو اختيار رقم من القائمة.")

if __name__ == "__main__":
    main()