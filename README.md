# برنامج تسيير السانديك (Syndic Manager)

## وصف البرنامج
برنامج تسيير السانديك هو تطبيق بسيط مكتوب بلغة Python لإدارة مساهمات ومصروفات السانديك (نقابة الملاك) في العمارات السكنية. يساعد هذا البرنامج في تنظيم المعلومات المتعلقة بالسكان، وتتبع المساهمات المالية، وتسجيل المصروفات، وإنشاء تقارير مالية بسيطة.

## الميزات
- إدارة قائمة السكان (إضافة وعرض)
- تسجيل المساهمات المالية لكل ساكن
- تسجيل المصروفات مع التواريخ والوصف
- عرض التقارير المالية (مجموع المساهمات، المصروفات، والرصيد)
- حفظ البيانات في ملف JSON واستعادتها عند إعادة تشغيل البرنامج

## طريقة الاستخدام
1. قم بتشغيل البرنامج باستخدام:
   ```
   python run_syndic.py
   ```
2. استخدم القائمة الرئيسية للتنقل بين مختلف الوظائف:
   - إضافة ساكن جديد
   - عرض قائمة السكان
   - تسجيل مساهمة مالية
   - إضافة مصروف جديد
   - عرض قائمة المصروفات
   - عرض التقرير المالي

## متطلبات التشغيل
- Python 3.6 أو أحدث
- لا توجد مكتبات خارجية مطلوبة (يستخدم البرنامج فقط المكتبات القياسية: json و os)

## ملاحظات
- يتم تخزين جميع البيانات في ملف `syndic_data.json` في نفس مجلد البرنامج
- البرنامج مصمم بواجهة نصية بسيطة وباللغة العربية
- إذا واجهت مشاكل مع دعم اللغة العربية، يمكنك استخدام النسخة الإنجليزية: `run_syndic_en.py`

---

# Syndic Manager Application

## Description
Syndic Manager is a simple Python application for managing homeowners' association finances in residential buildings. It helps organize resident information, track financial contributions, record expenses, and generate simple financial reports.

## Features
- Manage resident list (add and view)
- Record financial contributions for each resident
- Record expenses with dates and descriptions
- Display financial reports (total contributions, expenses, and balance)
- Save data to a JSON file and restore it when restarting the application

## How to Use
1. Run the application using one of these options:
   ```
   # Arabic version
   python run_syndic.py
   
   # English version (if you have issues with Arabic)
   python run_syndic_en.py
   ```
2. Use the main menu to navigate between different functions:
   - Add new resident
   - View resident list
   - Record financial contribution
   - Add new expense
   - View expense list
   - Display financial report

## Requirements
- Python 3.6 or newer
- No external libraries required (the program uses only standard libraries: json and os)

## Notes
- All data is stored in the `syndic_data.json` file in the same folder as the program
- The program has both Arabic and English interface options
- The English version (`run_syndic_en.py`) is provided for systems that may have trouble displaying Arabic text