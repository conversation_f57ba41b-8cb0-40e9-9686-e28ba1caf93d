#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Syndic Manager - Version 1.0
Application for managing syndic (homeowners association) contributions and expenses
"""

import sys
import io
from syndic_manager import main

if __name__ == "__main__":
    # Fix for Windows Command Prompt encoding issues
    try:
        # Try to set stdout to use utf-8 encoding
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        print("مرحبا بكم في برنامج تسيير السانديك - النسخة 1.0")
        print("تطبيق لإدارة مساهمات ومصروفات السانديك (نقابة الملاك) في العمارات السكنية")
    except:
        # Fallback to English if encoding fails
        print("Welcome to Syndic Manager - Version 1.0")
        print("Application for managing homeowners association contributions and expenses")
    
    print("=" * 60)
    main()